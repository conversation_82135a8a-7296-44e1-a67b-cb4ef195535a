/**
 * Alarm Manager for Smart AI Generation Alert Extension
 * Handles core alarm logic, state management, and timer operations
 */
import { AlarmConfig } from './types';
/**
 * AlarmManager class handles the core business logic
 * TODO: Implement in subsequent task "Implement Core Alarm Management Logic"
 */
export declare class AlarmManager {
    private state;
    private config;
    constructor(config: AlarmConfig);
    /**
     * Handle code change events
     * TODO: Implement code change handling and timer start
     */
    handleCodeChange(): void;
    /**
     * Handle user activity events (cursor movement, additional edits)
     * TODO: Implement user activity handling and timer reset
     */
    handleUserActivity(): void;
    /**
     * Handle terminal focus events
     * TODO: Implement terminal focus handling and suppression logic
     */
    handleTerminalFocus(): void;
    /**
     * Start alarm countdown timer
     * TODO: Implement countdown timer logic
     */
    private startAlarmCountdown;
    /**
     * Clear existing alarm timer
     * TODO: Implement timer cleanup
     */
    private clearExistingTimer;
    /**
     * Run alarm checks when timer expires
     * TODO: Implement suppression condition evaluation
     */
    private runAlarmChecks;
    /**
     * Check if immediate terminal use should suppress alarm
     * TODO: Implement immediate terminal use check
     */
    private isImmediateTerminalUse;
    /**
     * Check if recent terminal use should suppress alarm
     * TODO: Implement recent terminal use check
     */
    private isRecentTerminalUse;
    /**
     * Trigger the alarm
     * TODO: Implement alarm triggering
     */
    private triggerAlarm;
    /**
     * Update configuration
     */
    updateConfiguration(config: AlarmConfig): void;
    /**
     * Dispose and cleanup
     */
    dispose(): void;
}
//# sourceMappingURL=alarmManager.d.ts.map