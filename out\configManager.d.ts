/**
 * Configuration Manager for Smart AI Generation Alert Extension
 * Handles loading, validation, and monitoring of user settings
 */
import { AlarmConfig } from './types';
/**
 * ConfigManager class handles all configuration-related operations
 * TODO: Implement in subsequent task "Implement Configuration Management System"
 */
export declare class ConfigManager {
    private config;
    private configChangeListener;
    constructor();
    /**
     * Get current configuration
     * TODO: Implement configuration loading from VSCode settings
     */
    getConfiguration(): AlarmConfig;
    /**
     * Get countdown seconds setting
     * TODO: Implement with validation and fallback
     */
    getCountdownSeconds(): number;
    /**
     * Get terminal threshold seconds setting
     * TODO: Implement with validation and fallback
     */
    getTerminalThresholdSeconds(): number;
    /**
     * Get recent terminal threshold minutes setting
     * TODO: Implement with validation and fallback
     */
    getRecentTerminalThresholdMinutes(): number;
    /**
     * Check if extension is enabled
     * TODO: Implement with validation and fallback
     */
    isEnabled(): boolean;
    /**
     * Register configuration change listener
     * TODO: Implement configuration change monitoring
     */
    onConfigurationChanged(callback: () => void): void;
    /**
     * Validate configuration values
     * TODO: Implement configuration validation
     */
    private validateConfiguration;
    /**
     * Dispose configuration change listener
     */
    dispose(): void;
}
//# sourceMappingURL=configManager.d.ts.map