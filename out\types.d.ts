/**
 * Type definitions for Smart AI Generation Alert Extension
 */
/// <reference types="node" />
import * as vscode from 'vscode';
/**
 * Configuration interface for the extension settings
 */
export interface AlarmConfig {
    enabled: boolean;
    countdownSeconds: number;
    terminalUseThresholdSeconds: number;
    recentTerminalThresholdMinutes: number;
}
/**
 * Default configuration values
 */
export declare const DEFAULT_CONFIG: AlarmConfig;
/**
 * State interface for the AlarmManager
 */
export interface AlarmState {
    lastCodeChangeTimestamp: number | null;
    lastTerminalFocusTimestamp: number | null;
    alarmTimerId: NodeJS.Timeout | null;
    isExtensionActive: boolean;
}
/**
 * Event handler disposables interface
 */
export interface EventDisposables {
    textDocumentChange: vscode.Disposable | null;
    editorSelectionChange: vscode.Disposable | null;
    terminalFocusChange: vscode.Disposable | null;
    configurationChange: vscode.Disposable | null;
}
/**
 * Sound playback strategy enum
 */
export declare enum SoundStrategy {
    SYSTEM_BELL = "system_bell",
    NOTIFICATION_API = "notification_api",
    PLATFORM_COMMAND = "platform_command"
}
/**
 * Platform detection enum
 */
export declare enum Platform {
    WINDOWS = "win32",
    MACOS = "darwin",
    LINUX = "linux"
}
/**
 * Extension context interface
 */
export interface ExtensionContext {
    configManager: any;
    alarmManager: any;
    eventHandlers: any;
    soundManager: any;
}
/**
 * Logging levels
 */
export declare enum LogLevel {
    DEBUG = "debug",
    INFO = "info",
    WARN = "warn",
    ERROR = "error"
}
//# sourceMappingURL=types.d.ts.map