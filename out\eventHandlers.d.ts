/**
 * Event Handlers for Smart AI Generation Alert Extension
 * Manages VSCode event listeners and routing
 */
/**
 * EventHandlers class manages all VSCode event listeners
 * TODO: Implement in subsequent task "Implement Core Event Handling System"
 */
export declare class EventHandlers {
    private disposables;
    private alarmManager;
    constructor(alarmManager: any);
    /**
     * Set up all event listeners
     * TODO: Implement event listener registration
     */
    setupEventListeners(): void;
    /**
     * Handle text document change events
     * TODO: Implement text document change handling
     */
    private handleTextDocumentChange;
    /**
     * Handle editor selection change events
     * TODO: Implement editor selection change handling
     */
    private handleEditorSelectionChange;
    /**
     * Handle terminal focus change events
     * TODO: Implement terminal focus change handling
     */
    private handleTerminalFocusChange;
    /**
     * Filter document changes to exclude non-relevant changes
     * TODO: Implement document change filtering
     */
    private isRelevantDocumentChange;
    /**
     * Dispose all event listeners
     */
    dispose(): void;
}
//# sourceMappingURL=eventHandlers.d.ts.map