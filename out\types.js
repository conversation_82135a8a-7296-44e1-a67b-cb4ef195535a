"use strict";
/**
 * Type definitions for Smart AI Generation Alert Extension
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogLevel = exports.Platform = exports.SoundStrategy = exports.DEFAULT_CONFIG = void 0;
/**
 * Default configuration values
 */
exports.DEFAULT_CONFIG = {
    enabled: true,
    countdownSeconds: 15,
    terminalUseThresholdSeconds: 10,
    recentTerminalThresholdMinutes: 1
};
/**
 * Sound playback strategy enum
 */
var SoundStrategy;
(function (SoundStrategy) {
    SoundStrategy["SYSTEM_BELL"] = "system_bell";
    SoundStrategy["NOTIFICATION_API"] = "notification_api";
    SoundStrategy["PLATFORM_COMMAND"] = "platform_command";
})(SoundStrategy = exports.SoundStrategy || (exports.SoundStrategy = {}));
/**
 * Platform detection enum
 */
var Platform;
(function (Platform) {
    Platform["WINDOWS"] = "win32";
    Platform["MACOS"] = "darwin";
    Platform["LINUX"] = "linux";
})(Platform = exports.Platform || (exports.Platform = {}));
/**
 * Logging levels
 */
var LogLevel;
(function (LogLevel) {
    LogLevel["DEBUG"] = "debug";
    LogLevel["INFO"] = "info";
    LogLevel["WARN"] = "warn";
    LogLevel["ERROR"] = "error";
})(LogLevel = exports.LogLevel || (exports.LogLevel = {}));
//# sourceMappingURL=types.js.map