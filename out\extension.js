"use strict";
/**
 * Smart AI Generation Alert Extension
 * Main extension entry point
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const types_1 = require("./types");
// Extension components (will be imported when implemented)
// import { ConfigManager } from './configManager';
// import { AlarmManager } from './alarmManager';
// import { EventHandlers } from './eventHandlers';
// import { SoundManager } from './soundManager';
let extensionContext = null;
/**
 * Extension activation function
 * Called when the extension is activated
 */
function activate(context) {
    console.log('Smart AI Generation Alert Extension is now active!');
    try {
        // Initialize extension context
        extensionContext = {
            configManager: null,
            alarmManager: null,
            eventHandlers: null,
            soundManager: null
        };
        // TODO: Initialize components in subsequent tasks
        // 1. Initialize ConfigManager and load settings
        // 2. Create AlarmManager instance with configuration
        // 3. Set up EventHandlers and register all listeners
        // 4. Register configuration change handlers
        // 5. Add all disposables to extension context for cleanup
        // Register extension commands
        registerCommands(context);
        // Log successful activation
        logMessage(types_1.LogLevel.INFO, 'Extension activated successfully');
    }
    catch (error) {
        logMessage(types_1.LogLevel.ERROR, `Failed to activate extension: ${error}`);
        vscode.window.showErrorMessage('Smart AI Generation Alert: Failed to activate extension');
    }
}
exports.activate = activate;
/**
 * Extension deactivation function
 * Called when the extension is deactivated
 */
function deactivate() {
    console.log('Smart AI Generation Alert Extension is being deactivated');
    try {
        // TODO: Implement cleanup in subsequent tasks
        // 1. Dispose all event listeners
        // 2. Clear any active timers
        // 3. Cleanup AlarmManager state
        extensionContext = null;
        logMessage(types_1.LogLevel.INFO, 'Extension deactivated successfully');
    }
    catch (error) {
        logMessage(types_1.LogLevel.ERROR, `Error during deactivation: ${error}`);
    }
}
exports.deactivate = deactivate;
/**
 * Register extension commands
 */
function registerCommands(context) {
    // Test sound command
    const testSoundCommand = vscode.commands.registerCommand('aiAlert.testSound', () => {
        // TODO: Implement sound testing when SoundManager is available
        vscode.window.showInformationMessage('Test sound functionality will be implemented in subsequent tasks');
    });
    // Toggle enabled command
    const toggleEnabledCommand = vscode.commands.registerCommand('aiAlert.toggleEnabled', async () => {
        const config = vscode.workspace.getConfiguration('aiAlert');
        const currentEnabled = config.get('enabled', true);
        await config.update('enabled', !currentEnabled, vscode.ConfigurationTarget.Global);
        const status = !currentEnabled ? 'enabled' : 'disabled';
        vscode.window.showInformationMessage(`Smart AI Generation Alert ${status}`);
    });
    // Add commands to context for disposal
    context.subscriptions.push(testSoundCommand, toggleEnabledCommand);
}
/**
 * Utility function for logging messages
 */
function logMessage(level, message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] [${level.toUpperCase()}] Smart AI Alert: ${message}`;
    switch (level) {
        case types_1.LogLevel.DEBUG:
        case types_1.LogLevel.INFO:
            console.log(logEntry);
            break;
        case types_1.LogLevel.WARN:
            console.warn(logEntry);
            break;
        case types_1.LogLevel.ERROR:
            console.error(logEntry);
            break;
    }
}
//# sourceMappingURL=extension.js.map